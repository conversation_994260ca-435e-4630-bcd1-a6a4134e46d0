import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payslip";
import { eq } from "drizzle-orm";

// Fetch all deductions item types
ipcMain.handle(
  "employerDb:getDeductionsItemTypes",
  async (_event, dbPath: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      let itemTypes = await db
        .select()
        .from(schema.payslipDeductionsItemTypes)
        .all();

      // Seed default types if none exist
      if (itemTypes.length === 0) {
        const now = Math.floor(Date.now() / 1000);
        const defaults = [
          {
            id: "income-tax",
            code: "income-tax",
            display_label: "Income Tax",
            created_at: now,
            updated_at: now,
          },
          {
            id: "national-insurance",
            code: "national-insurance",
            display_label: "National Insurance",
            created_at: now,
            updated_at: now,
          },
          {
            id: "pension",
            code: "pension",
            display_label: "Pension Contributions",
            created_at: now,
            updated_at: now,
          },
          {
            id: "student-loan",
            code: "student-loan",
            display_label: "Student Loan",
            created_at: now,
            updated_at: now,
          },
          {
            id: "court-order",
            code: "court-order",
            display_label: "Court Orders",
            created_at: now,
            updated_at: now,
          },
          {
            id: "union-dues",
            code: "union-dues",
            display_label: "Union Dues",
            created_at: now,
            updated_at: now,
          },
          {
            id: "other",
            code: "other",
            display_label: "Other Deductions",
            created_at: now,
            updated_at: now,
          },
        ];
        await db
          .insert(schema.payslipDeductionsItemTypes)
          .values(defaults)
          .run();
        itemTypes = await db
          .select()
          .from(schema.payslipDeductionsItemTypes)
          .all();
      }
      return { success: true, itemTypes };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Upsert a deductions line item
ipcMain.handle(
  "employerDb:upsertPayslipDeductionsLineItem",
  async (_event, dbPath: string, item: any) => {
    try {
      console.log(
        "[IPC] Upserting deductions line item:",
        JSON.stringify(item, null, 2),
      );
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);

      if (!item.id) {
        console.log("[IPC] Creating new deductions line item");
        const id = crypto.randomUUID();
        const [inserted] = await db
          .insert(schema.payslipDeductionsLineItems)
          .values({ ...item, id, created_at: now, updated_at: now })
          .returning();
        console.log("[IPC] Created deductions line item:", inserted);
        return { success: true, lineItem: inserted };
      } else {
        console.log("[IPC] Updating existing deductions line item:", item.id);
        const [updated] = await db
          .update(schema.payslipDeductionsLineItems)
          .set({ ...item, updated_at: now })
          .where(eq(schema.payslipDeductionsLineItems.id, item.id))
          .returning();
        console.log("[IPC] Updated deductions line item:", updated);
        return { success: true, lineItem: updated };
      }
    } catch (err: any) {
      console.error("[IPC] Error upserting deductions line item:", err);
      return { success: false, error: err.message };
    }
  },
);

// Delete a deductions line item
ipcMain.handle(
  "employerDb:deletePayslipDeductionsLineItem",
  async (_event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.id, id));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Clear all deductions line items (set amounts to 0)
ipcMain.handle(
  "employerDb:clearAllPayslipDeductionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);
      const result = await db
        .update(schema.payslipDeductionsLineItems)
        .set({ amount: 0, updated_at: now })
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslipId));
      return { success: true, updatedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete all deductions line items
ipcMain.handle(
  "employerDb:deleteAllPayslipDeductionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslipId));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);
